<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>钱包解绑管理</span>
      </div>

      <!-- 查询表单 -->
      <el-form ref="queryForm" :model="queryForm" :rules="queryRules" label-width="120px" class="query-form">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="HandleName" prop="handleName">
              <el-input
                v-model="queryForm.handleName"
                placeholder="请输入用户handleName"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="WalletAddress" prop="walletAddress">
              <el-input
                v-model="queryForm.walletAddress"
                placeholder="请输入钱包地址"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item>
              <el-button type="primary" :loading="queryLoading" @click="handleQuery">
                <i class="el-icon-search" /> 查询绑定关系
              </el-button>
              <el-button @click="handleReset">
                <i class="el-icon-refresh" /> 重置
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <!-- 绑定关系展示 -->
      <div v-if="bindingInfo" class="binding-info">
        <el-divider content-position="left">绑定关系信息</el-divider>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="passportId">{{ bindingInfo.passportId }}</el-descriptions-item>
          <el-descriptions-item label="钱包地址">{{ bindingInfo.walletAddress }}</el-descriptions-item>
          <el-descriptions-item label="绑定类型">
            {{ bindingInfo.connectType === 'KEY' ? 'KEY' : 'BIND' }}
          </el-descriptions-item>
          <el-descriptions-item label="绑定时间">{{ bindingInfo.connectedAt }}</el-descriptions-item>
        </el-descriptions>

        <!-- 解绑操作 -->
        <div class="unbind-actions">
          <el-button
            type="danger"
            :loading="unbindLoading"
            :disabled="!bindingInfo"
            @click="handleUnbind"
          >
            <i class="el-icon-delete" /> 解绑钱包
          </el-button>
        </div>
      </div>

      <!-- 无绑定关系提示 -->
      <div v-else-if="queryExecuted && !bindingInfo" class="no-binding">
        <el-empty description="未找到绑定关系">
          <el-button type="primary" @click="handleReset">重新查询</el-button>
        </el-empty>
      </div>
    </el-card>
  </div>
</template>

<script>
import { queryWalletBinding, unbindWallet } from '@/api/drex/wallet/wallet'

export default {
  name: 'WalletUnbind',
  data() {
    return {
      queryForm: {
        handleName: '',
        walletAddress: ''
      },
      queryRules: {
        handleName: [
          { required: true, message: '请输入用户handleName', trigger: 'blur' }
        ],
        walletAddress: [
          { required: true, message: '请输入钱包地址', trigger: 'blur' }
        ]
      },
      bindingInfo: null,
      queryLoading: false,
      unbindLoading: false,
      queryExecuted: false
    }
  },
  methods: {
    // 查询绑定关系
    handleQuery() {
      this.$refs.queryForm.validate((valid) => {
        if (valid) {
          this.queryLoading = true
          this.queryExecuted = false

          queryWalletBinding({
            handleName: this.queryForm.handleName,
            walletAddress: this.queryForm.walletAddress
          }).then(response => {
            this.queryExecuted = true
            if (response.bindings) {
              this.bindingInfo = response.bindings
              this.$message.success('查询成功')
            } else {
              this.bindingInfo = null
              this.$message.warning('未找到绑定关系')
            }
          }).catch(error => {
            this.queryExecuted = true
            this.bindingInfo = null
            this.$message.error('查询失败：' + (error.message || '未知错误'))
          }).finally(() => {
            this.queryLoading = false
          })
        }
      })
    },

    // 解绑钱包
    handleUnbind() {
      this.$confirm('确定要解绑该钱包吗？解绑后将无法恢复！', '确认解绑', {
        confirmButtonText: '确定解绑',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.unbindLoading = true

        unbindWallet({
          passportId: this.bindingInfo.passportId,
          walletAddress: this.bindingInfo.walletAddress
        }).then(response => {
          if (response === true) {
            this.$message.success('解绑成功，可以重新查询确认是否删除')
            // 解绑成功后只清空绑定信息，保留输入框内容
            this.bindingInfo = null
            this.queryExecuted = false
          } else {
            this.$message.error('解绑失败：' + (response.message || '未知错误'))
          }
        }).catch(error => {
          this.$message.error('解绑失败：' + (error.message || '未知错误'))
        }).finally(() => {
          this.unbindLoading = false
        })
      })
    },

    // 重置表单
    handleReset() {
      this.$refs.queryForm.resetFields()
      this.bindingInfo = null
      this.queryExecuted = false
    }
  }
}
</script>

<style scoped>
.query-form {
  margin-bottom: 20px;
}

.binding-info {
  margin-top: 20px;
}

.unbind-actions {
  margin-top: 20px;
  text-align: center;
}

.no-binding {
  margin-top: 20px;
  text-align: center;
}

.box-card {
  margin: 20px;
}
</style>
